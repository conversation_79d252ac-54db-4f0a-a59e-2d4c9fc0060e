package web

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gateway/internal/config"
	"gateway/internal/platformRequest"
	"gateway/internal/utils"
	"io/fs"
	golog "log"
	"net/http"
	"os"
	"path/filepath"

	"github.com/go-chi/render"
	"github.com/gorilla/mux"
)

// ActivationServer handles device activation in simplified mode
type ActivationServer struct {
	router     *mux.Router
	httpServer *http.Server
	shutdownCh chan struct{}
}

// ActivationRequest represents the activation request payload
type ActivationRequest struct {
	DeviceCode   string `json:"device_code"`
	Secret       string `json:"secret"`
	CloudGateway string `json:"cloud_gateway"`
}

// ActivationResponse represents the activation response
type ActivationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// NewActivationServer creates a new activation server
func NewActivationServer() *ActivationServer {
	router := mux.NewRouter()
	return &ActivationServer{
		router:     router,
		shutdownCh: make(chan struct{}),
	}
}

// Serve starts the activation server
func (s *ActivationServer) Serve() error {
	// Setup routes
	s.setupRoutes()

	// Create HTTP server
	logFile, _ := os.OpenFile(os.DevNull, os.O_WRONLY|os.O_CREATE, 0666)
	s.httpServer = &http.Server{
		Addr:     "0.0.0.0:3000",
		Handler:  s.router,
		ErrorLog: golog.New(logFile, "", 0),
	}

	// Start server in goroutine
	go func() {
		err := s.httpServer.ListenAndServe()
		if err != nil && !errors.Is(err, http.ErrServerClosed) {
			fmt.Printf("Start activation web server error: %s\n", err.Error())
		}
		close(s.shutdownCh)
	}()

	// Wait for shutdown signal
	<-s.shutdownCh
	return nil
}

// Shutdown gracefully shuts down the activation server
func (s *ActivationServer) Shutdown() error {
	if s.httpServer != nil {
		return s.httpServer.Shutdown(context.TODO())
	}
	return nil
}

// setupRoutes configures the activation server routes
func (s *ActivationServer) setupRoutes() {
	// Enable CORS for activation mode
	s.router.Use(corsMiddleware)

	// API routes for activation
	api := s.router.PathPrefix("/api").Subrouter()
	api.HandleFunc("/activate", s.handleActivation).Methods("POST")
	api.HandleFunc("/status", s.handleActivationStatus).Methods("GET")

	// Serve static files (embedded frontend)
	sub, err := fs.Sub(content, "content")
	if err != nil {
		fmt.Printf("Error loading embedded content: %s\n", err.Error())
		return
	}

	// Serve frontend files
	s.router.PathPrefix("/").Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		path := r.URL.Path
		if path == "" || path == "/" {
			path = "/index.html"
		}

		// Try to serve from embedded content
		file, err := sub.Open(path[1:])
		if err != nil {
			// If not found, serve index.html for SPA routing
			file, err = sub.Open("index.html")
			if err != nil {
				http.NotFound(w, r)
				return
			}
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
		}
		defer file.Close()

		http.FileServer(http.FS(sub)).ServeHTTP(w, r)
	}))
}

// handleActivation processes device activation requests
func (s *ActivationServer) handleActivation(w http.ResponseWriter, r *http.Request) {
	var req ActivationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.sendActivationResponse(w, r, false, "Invalid request format")
		return
	}

	// Validate required fields
	if req.DeviceCode == "" || req.Secret == "" || req.CloudGateway == "" {
		s.sendActivationResponse(w, r, false, "Missing required fields: device_code, secret, and cloud_gateway are required")
		return
	}

	// Create temporary config for validation
	tempConfig := config.GatewayConfig{
		DeviceCode:   req.DeviceCode,
		Secret:       req.Secret,
		CloudGateway: req.CloudGateway,
		// Set default values
		LogLevel:         "info",
		ClashLogLevel:    "error",
		ClashSecret:      "xrouter",
		ClashApiAddress:  "0.0.0.0:5123",
		ClashProxyPort:   5124,
		ClashFilePath:    "clash.yaml", // 添加clash文件路径，确保clash.yaml生成在当前目录
		TestDelayUrl:     "https://cp.cloudflare.com/generate_204",
		TestDelayTimeout: 10000,
		Domain:           "x.net",
		Flags: []string{
			"discard_enable",
			"wifi_disable",
			"esim_disable",
			"exclusive_disable",
		},
	}

	// Try to validate credentials with the platform
	if err := s.validateCredentials(tempConfig); err != nil {
		s.sendActivationResponse(w, r, false, fmt.Sprintf("Activation failed: %s", err.Error()))
		return
	}

	// Save configuration to file
	if err := s.saveConfig(tempConfig); err != nil {
		s.sendActivationResponse(w, r, false, fmt.Sprintf("Failed to save configuration: %s", err.Error()))
		return
	}

	s.sendActivationResponse(w, r, true, "Device activated successfully")
}

// handleActivationStatus returns the current activation status
func (s *ActivationServer) handleActivationStatus(w http.ResponseWriter, r *http.Request) {
	configPath := filepath.Join(utils.GetEtcDir(), "config-local.json")

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		render.JSON(w, r, map[string]interface{}{
			"activated": false,
			"message":   "Device not activated",
		})
		return
	}

	render.JSON(w, r, map[string]interface{}{
		"activated": true,
		"message":   "Device is activated",
	})
}

// validateCredentials validates the device credentials with the platform
func (s *ActivationServer) validateCredentials(cfg config.GatewayConfig) error {
	// Create a temporary config file for validation
	tempConfigPath := filepath.Join(utils.GetRunDir(), "temp-config.json")

	// Save temp config
	configData, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(tempConfigPath, configData, 0644); err != nil {
		return fmt.Errorf("failed to write temp config: %w", err)
	}
	defer os.Remove(tempConfigPath) // Clean up

	// Set the config path temporarily
	config.SetConfigPath(tempConfigPath)
	defer func() {
		// Restore original config path
		config.SetConfigPath(filepath.Join(utils.GetEtcDir(), "config-local.json"))
	}()

	// Load and validate config
	if err := config.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Try to initialize platform request (this validates credentials)
	if err := platformRequest.Init(); err != nil {
		return fmt.Errorf("credential validation failed: %w", err)
	}

	return nil
}

// saveConfig saves the validated configuration to the system path
func (s *ActivationServer) saveConfig(cfg config.GatewayConfig) error {
	configPath := filepath.Join(utils.GetEtcDir(), "config-local.json")

	// Ensure the directory exists
	if err := os.MkdirAll(filepath.Dir(configPath), 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Marshal configuration
	configData, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// Write configuration file
	if err := os.WriteFile(configPath, configData, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	fmt.Printf("Configuration saved to: %s\n", configPath)
	return nil
}

// sendActivationResponse sends a JSON response for activation requests
func (s *ActivationServer) sendActivationResponse(w http.ResponseWriter, r *http.Request, success bool, message string) {
	response := ActivationResponse{
		Success: success,
		Message: message,
	}

	if success {
		render.Status(r, http.StatusOK)
	} else {
		render.Status(r, http.StatusBadRequest)
	}

	render.JSON(w, r, response)
}

// corsMiddleware enables CORS for activation mode
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}
