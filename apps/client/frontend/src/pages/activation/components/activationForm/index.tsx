import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON>ir<PERSON>, Loader2 } from "lucide-react";

interface ActivationFormProps {
    onSuccess: () => void;
}

interface ActivationData {
    device_code: string;
    secret: string;
    cloud_gateway: string;
}

interface ActivationResponse {
    success: boolean;
    message: string;
}

export function ActivationForm({ onSuccess }: ActivationFormProps) {
    const [formData, setFormData] = useState<ActivationData>({
        device_code: "",
        secret: "",
        cloud_gateway: "http://***********:8079/manager/",
    });
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const handleInputChange = (field: keyof ActivationData) =>
    (
        e: React.ChangeEvent<HTMLInputElement>,
    ) => {
        setFormData((prev) => ({
            ...prev,
            [field]: e.target.value,
        }));
        // Clear error when user starts typing
        if (error) {
            setError(null);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate form
        if (
            !formData.device_code.trim() || !formData.secret.trim() ||
            !formData.cloud_gateway.trim()
        ) {
            setError("All fields are required");
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            const response = await fetch("/api/activate", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(formData),
            });

            const data = await response.json() as ActivationResponse;

            if (data.success) {
                onSuccess();
            } else {
                setError(data.message || "Activation failed");
            }
        } catch (err) {
            setError("Network error: Failed to connect to server");
            console.error("Activation error:", err);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            <div className="space-y-2">
                <Label htmlFor="device_code">Device Code</Label>
                <Input
                    id="device_code"
                    type="text"
                    placeholder="Enter your device code"
                    value={formData.device_code}
                    onChange={handleInputChange("device_code")}
                    disabled={isLoading}
                    required
                />
                <p className="text-xs text-gray-500">
                    The device code provided by your administrator
                </p>
            </div>

            <div className="space-y-2">
                <Label htmlFor="secret">Secret</Label>
                <Input
                    id="secret"
                    type="password"
                    placeholder="Enter your secret"
                    value={formData.secret}
                    onChange={handleInputChange("secret")}
                    disabled={isLoading}
                    required
                />
                <p className="text-xs text-gray-500">
                    The secret key provided by your administrator
                </p>
            </div>

            <div className="space-y-2">
                <Label htmlFor="cloud_gateway">Cloud Gateway URL</Label>
                <Input
                    id="cloud_gateway"
                    type="url"
                    placeholder="http://example.com/manager/"
                    value={formData.cloud_gateway}
                    onChange={handleInputChange("cloud_gateway")}
                    disabled={isLoading}
                    required
                />
                <p className="text-xs text-gray-500">
                    The cloud gateway URL for your organization
                </p>
            </div>

            <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
            >
                {isLoading
                    ? (
                        <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Activating...
                        </>
                    )
                    : (
                        "Activate Device"
                    )}
            </Button>
        </form>
    );
}
