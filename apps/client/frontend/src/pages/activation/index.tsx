import { useEffect, useState } from "react";
import { ActivationForm } from "./components/activationForm";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, CheckCircle } from "lucide-react";

interface ActivationStatus {
    activated: boolean;
    message: string;
}

export function ActivationPage() {
    const [activationStatus, setActivationStatus] = useState<
        ActivationStatus | null
    >(null);
    const [isLoading, setIsLoading] = useState(true);

    // Check activation status on component mount
    useEffect(() => {
        checkActivationStatus();
    }, []);

    const checkActivationStatus = async () => {
        try {
            const response = await fetch("/api/status");
            const data = await response.json() as ActivationStatus;
            setActivationStatus(data);

            // If already activated, redirect to dashboard after a short delay
            if (data.activated) {
                setTimeout(() => {
                    // 确保使用 127.0.0.1 而不是 localhost 进行跳转
                    window.location.href = "http://127.0.0.1:3000/dashboard";
                }, 2000);
            }
        } catch (error) {
            console.error("Failed to check activation status:", error);
            setActivationStatus({
                activated: false,
                message: "Failed to check activation status",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleActivationSuccess = () => {
        // Refresh status after successful activation
        checkActivationStatus();
    };

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto">
                    </div>
                    <p className="mt-4 text-gray-600">
                        Checking activation status...
                    </p>
                </div>
            </div>
        );
    }

    if (activationStatus?.activated) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                            <CheckCircle className="h-6 w-6 text-green-600" />
                        </div>
                        <CardTitle className="text-green-800">
                            Device Activated
                        </CardTitle>
                        <CardDescription>
                            Your device has been successfully activated and
                            configured.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                                Redirecting to dashboard in a few seconds...
                            </AlertDescription>
                        </Alert>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <h2 className="mt-6 text-3xl font-bold text-gray-900">
                        Device Activation
                    </h2>
                    <p className="mt-2 text-sm text-gray-600">
                        Please enter your device credentials to activate this
                        gateway
                    </p>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Activate Your Device</CardTitle>
                        <CardDescription>
                            Enter your device code, secret, and cloud gateway
                            URL to get started.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ActivationForm onSuccess={handleActivationSuccess} />
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
