import { ApiResponseSchema } from "@/api/commonProxy";
import { z } from "zod";

export const PathSchema = z.object({
	name: z.string(),
	proxies: z.array(z.string()),
	use: z.boolean(),
	proxies_code: z.array(z.string()),
	current_ip_use: z.boolean(),
	change_time: z.number(),
	change_country_array: z.union([z.array(z.array(z.string())), z.null()]),
	total_round_num: z.number().optional(),
	current_round_num: z.number().optional(),
});

export const PathListSchema = ApiResponseSchema(z.array(PathSchema));

export type PathList = z.infer<typeof PathSchema>;

export const UserSchema = z.object({
	account: z.string(),
	created_at: z.string(),
	last_login_at: z.string(),
	id: z.string(),
	is_active: z.boolean(),
	is_admin: z.boolean(),
});

export const ApiUserSchema = ApiResponseSchema(UserSchema);

export const UserListSchema = ApiResponseSchema(z.array(UserSchema));

export const RegisterSchema = ApiResponseSchema(
	z.object({
		id: z.string(),
	}),
);

export type UserList = z.infer<typeof UserSchema>;

export const ProxiesSchema = z.object({
	city_name: z.string(),
	country_code: z.string(),
	country_name: z.string(),
	delay: z.number(),
	name: z.string(),
	port: z.number(),
	protocol: z.string(),
	server: z.string(),
	type: z.string(),
	use_status: z.string(),
	delayStyle: z.string().nullable().optional(),
	i: z.number().optional(),
});

export const ProxiesListSchema = ApiResponseSchema(z.array(ProxiesSchema));

export type ProxiesList = z.infer<typeof ProxiesSchema>;

export const ProxiesFastestSchema = z.array(z.string());
export const ProxiesFastestListSchema = ApiResponseSchema(ProxiesFastestSchema);
export type ProxiesFastest = z.infer<typeof ProxiesFastestSchema>;
