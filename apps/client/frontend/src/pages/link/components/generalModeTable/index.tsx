import {
	forwardRef,
	use<PERSON><PERSON>back,
	useEffect,
	useImper<PERSON><PERSON><PERSON><PERSON>,
	useMemo,
	useState,
} from "react";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import _ from "lodash";
import { Form } from "antd";
import { But<PERSON> } from "@/components/ui/button";
import { DataTableMemo } from "@/components/LqTable/data-table-scroll";
import { DataTableViewOptions } from "@/components/LqTable/components/data-table-view-options";
import { columns } from "./columns";
import { UserManageEnum } from "../../enums";
import { FormAlertDialog } from "../formAlertDialog";
import { DialogConfig, DIALOGTYPE } from "../../data/type";
import {
	useLink,
	usePathDeleteMutation,
	usePathListMutation,
} from "@/api/link";
import { PathList } from "../../types/proxy-bridge-type";
import { SearchLocal } from "./search-local";
import { useUserList } from "@/api/user";
import { StateTag } from "@/components/StateTag";
import { errorToast, successToast } from "@/components/GlobalToast";
import { toast } from "@/components/ui/use-toast";
import UseDialog from "@/components/UseDialog";
import DeleteDialog from "@/components/DeleteDialog";
import { isAdmin, isWretchError } from "@/auth";
// import { DataTableColumnHeader } from "@/components/LqTable/components/data-table-column-header";
import { PathTosat } from "../pathTosat";
import { OrdersType } from "@/components/LqTable/@types/common";

import { getProxies } from "@/api/proxies";
// import { startCountry } from "@/pages/data";

interface IProps {
	onData: (data: PathList[]) => void;
}

// 定义子组件暴露给父组件的接口类型
export interface GeneralModeTableRef {
	clear: () => void;
	setOpenFormAlertDialog: (open: boolean) => void;
	setTypeFormAlertDialog: (type: DialogConfig) => void;
}

export interface LinkType {
	contry: string;
	flag: string;
	exclusive?: string;
	disabled?: boolean;
	hideExclusiveStyle?: boolean;
}

export interface DialogProps extends PathList {
	name: string;
	nodeGuard: string;
	nodeExit: string;
	nodeDefault: string;
	use: boolean;
	exclusive: string | boolean;
	jump: boolean;
	time: string[];
	change_time: number;
	change_country_array: string[][] | null;
}

const formInit: DialogProps = {
	name: "",
	nodeGuard: "",
	nodeExit: "",
	nodeDefault: "",
	use: false,
	exclusive: false,
	proxies: [],
	proxies_code: [],
	current_ip_use: false,
	jump: false,
	time: ["", ""],
	change_time: 0,
	change_country_array: null,
	total_round_num: 0,
	current_round_num: 0,
};
export const GeneralModeTable = forwardRef<GeneralModeTableRef, IProps>(
	({ onData }, ref) => {
		const { globalInfo } = useGlobalInfoContext();

		const [pagination] = useState({
			currentPage: 1,
			pageSize: 999,
			totalCount: 0,
		});
		const [open, setOpen] = useState<boolean>(false);
		const [hintOpen, setHintOpen] = useState<boolean | null>(null);
		const [result, setResult] = useState<boolean | null>(null);
		const [useVisible, setUseVisible] = useState(false);
		const [useLoading, setUseLoading] = useState(false);
		const [useData, setUseData] = useState<PathList>();
		const [delVisible, setDelVisible] = useState(false);
		const [deleteLoading, setDeleteLoading] = useState(false);
		const [deleteData, setDeleteData] = useState<PathList>();
		const [dialogLoading] = useState(false);
		const [type, setType] = useState<DialogConfig>(DIALOGTYPE.ADD);
		const [currentRow, setCurrentRow] = useState<PathList>();
		const [orders, setOrders] = useState<OrdersType>();

		const {
			data: pathLink,
			isLoading: pathLinkLoading,
			error: pathLinkError,
		} = useLink();
		const { data: userList } = (isAdmin() && useUserList()) || {};

		const { data: allProxies } = getProxies();
		const pathListMutation = usePathListMutation();
		const pathDeleteMutation = usePathDeleteMutation();

		const [form] = Form.useForm();

		const isWg = useMemo(
			() => false, // WG functionality removed
			[globalInfo],
		);
		const flags = useMemo(() => {
			return globalInfo?.data.flags || [];
		}, [globalInfo]);

		const exclusive_num = useMemo(() => {
			return globalInfo?.data?.exclusive_num || 0;
		}, [globalInfo]);

		const onSuccess = () => {
			setResult?.(true);
			successToast({ title: "修改成功" }, toast);
		};
		const errorTosatShow = (error: unknown, errorType?: string) => {
			const res = isWretchError(error) ? error.json : undefined;

			const errorMessages = {
				"exclusive exceeds limit": () => setResult?.(false),
				"already use": () =>
					errorToast("当前链路出口节点已被使用，无法私有", toast),
				exclusive: () =>
					errorToast("当前链路出口节点已被私有，请修改后应用", toast),
				"proxy not found": () =>
					errorToast(
						"当前链路部分节点不存在，请重新配置后应用",
						toast,
					),
				"name unavailable": () =>
					errorToast("链路名称重复，请重新编辑名称", toast),
				"proxy is discard": () =>
					errorToast(
						"当前链路出口节点已被弃用，请重新配置后应用",
						toast,
					),
				default: () => errorToast(errorType ?? "修改失败", toast),
			};

			let matched = false;

			for (const [pattern, handler] of Object.entries(errorMessages)) {
				if (res?.msg.includes(pattern)) {
					handler();
					matched = true;
					break;
				}
			}
			if (!matched) {
				errorToast(errorType ?? "修改失败", toast);
			}
		};

		const onSwitchHandle = _.debounce(async (row: PathList) => {
			pathListMutation.mutate(
				{ ...row, use: !row.use }, // Toggle use instead of exclusive
				{ onSuccess, onError: (error) => errorTosatShow(error) },
			);
		}, 300);
		const onStateHandle = _.debounce((row: PathList) => {
			pathListMutation.mutate(
				{ ...row, apply: true },
				{ onSuccess, onError: (error) => errorTosatShow(error) },
			);
		}, 300);

		const sortBy = (data: PathList[], orders: OrdersType) => {
			const key = Object.keys(orders)[0];
			function getUse(v: PathList) {
				if (key === "proxies") {
					return v.change_time ? 1 : 2;
				}
				return v.current_ip_use ? 1 : v.use ? 2 : 3;
			}
			return data.sort((a, b) => {
				const useComparison = getUse(a) - getUse(b);
				return orders[key]?.desc ? -useComparison : useComparison;
			});
		};

		const tableData = useMemo(() => {
			if (pathLinkLoading) {
				return [];
			}
			if (pathLinkError) {
				console.error("Path link loading error:", pathLinkError);
				return [];
			}
			if (!pathLink || !pathLink.data) {
				console.warn("No path link data available");
				return [];
			}
			const pathLinkInfo = JSON.parse(JSON.stringify(pathLink)) as {
				data: PathList[];
			};
			const data = orders
				? sortBy(pathLinkInfo?.data || [], orders)
				: pathLinkInfo?.data;
			return data || [];
		}, [pathLink, orders, pathLinkLoading, pathLinkError]);

		const userFilter = useMemo(() => {
			// Account functionality removed - single user mode
			return [{
				label: "Single User",
				value: "single_user",
				icon: (
					<StateTag
						active={{
							className:
								" text-[#fff] leading-[14px] bg-[#065F46] text-xs mr-[2px]",
							desc: "管",
						}}
					/>
				),
			}];
		}, [userList, pathLink]);

		const canSubmit = useMemo(() => {
			return true;
		}, []);

		const successHandle = async () => {
			if (!canSubmit) return;
			await form.validateFields();
			const addvalue = form.getFieldsValue();
			const isAdd = type === DIALOGTYPE.ADD;
			const hasNewName = addvalue.name === currentRow?.name || isAdd;
			const titleType = isAdd ? "新增" : "编辑";

			// 修复动态跳变时间计算逻辑
			const time = addvalue.time?.map((v: any) => {
				const num = parseInt(v, 10);
				return isNaN(num) ? 0 : num;
			}) ?? [0, 0];

			const country =
				addvalue.change_country_array?.map((v: string[]) =>
					v && v.length ? v : ["all"]
				) ?? null;

			// 确保时间计算正确：小时*3600 + 分钟*60
			const changeTimeInSeconds = addvalue.jump
				? (time[0] || 0) * 3600 + (time[1] || 0) * 60
				: 0;

			const payload = {
				...addvalue,
				name: hasNewName ? addvalue.name : currentRow?.name,
				exclusive: addvalue.exclusive ? "out" : "none",
				proxies: [
					addvalue.nodeDefault,
					addvalue.nodeGuard,
					addvalue.nodeExit,
				],
				new_name: hasNewName ? "" : addvalue.name,
				apply: addvalue.use,
				change_time: changeTimeInSeconds,
				change_country_array: addvalue.jump ? country : null,
			};

			delete payload.jump;
			delete payload.time;

			pathListMutation.mutate(
				isAdd ? { ...payload, create: true } : payload,
				{
					onSuccess: () => {
						successToast({ title: titleType + "成功" }, toast);
						setResult?.(true);
					},
					onError: (error) => {
						errorTosatShow(error, titleType + "失败");
					},
				},
			);
			clear();
			setOpen(false);
		};

		const clear = () => {
			form.resetFields();
			form.setFieldsValue(formInit);
			setCurrentRow(undefined);
		};

		const handleDialog = (open: boolean) => {
			clear();
			setOpen(open);
		};
		const editHandle = (row: PathList) => {
			setType(DIALOGTYPE.EDIT);

			const hour = row.change_time > 0
				? Math.floor(row.change_time / 60 / 60).toString()
				: "";
			const minute = row.change_time > 0
				? ((row.change_time / 60) % 60).toString()
				: "";
			const time = [hour, minute];
			const currentData = {
				...row,
				exclusive: false, // Always false in single user mode
				nodeDefault: row.proxies[0] || "",
				nodeGuard: row.proxies[1] || "",
				nodeExit: row.proxies[2] || "",
				jump: row.change_time > 0,
				time,
				total_round_num: row.total_round_num || 0,
				current_round_num: row.current_round_num || 0,
			};
			form.setFieldsValue(currentData);
			setCurrentRow(row);
			setOpen(true);
		};
		const useHandle = _.debounce(
			useCallback(async () => {
				if (useLoading) return;
				setUseLoading(true);
				if (useData) {
					pathListMutation.mutate(
						{ ...useData, apply: true },
						{
							onSuccess: () => {
								successToast(
									"【" + useData?.name + "】链路应用成功！",
									toast,
								);
							},
							onError: (error) => {
								// if (isWretchError(error)) {
								//     // const res = err.json
								//     errorToast('【' + useData?.name + '】链路应用成功！', toast);

								// }

								errorTosatShow(error);
							},
						},
					);
				}
				setUseVisible(false);
				setUseLoading(false);
			}, [useData]),
			300,
		);

		const deleteHandle = _.debounce(
			useCallback(async () => {
				if (deleteLoading) return;
				setDeleteLoading(true);
				if (deleteData) {
					pathDeleteMutation.mutate(deleteData?.name, {
						onSuccess: () => {
							console.log("deleteData", deleteData);
							successToast(
								"【" + deleteData?.name + "】删除成功",
								toast,
							);
						},
						onError: (error) => {
							if (isWretchError(error)) {
								const res = error.json;
								if (res.msg.includes("path not found")) {
									errorToast("链路已被删除", toast);
								} else {
									errorToast("删除失败", toast);
								}
							}
						},
					});
				}
				setDelVisible(false);
				setDeleteLoading(false);
			}, [deleteData]),
			300,
		);

		const dialogProps = {
			open,
			setOpen: handleDialog,
			successHandle,
			dialogLoading,
			form,
			type,
			canSubmit,
			row: currentRow,
			proxiesList: allProxies?.data || [],
			initialValues:
				(type === DIALOGTYPE.ADD
					? formInit
					: currentRow) as DialogProps,
		};

		const columnsData = useMemo(() => {
			const columnList = columns({
				isSpecial: isWg,
				flags,
				exclusive_num,
				editHandle,
				onSwitchHandle,
				onStateHandle,
				setUseData,
				setUseVisible,
				setDeleteData,
				setDelVisible,
				setOrders,
				orders,
			});

			// if (isAdmin()) {
			// 	columnList.unshift({
			// 		accessorKey: "account",
			// 		header: ({ column }) => (
			// 			<DataTableColumnHeader column={column} title="用户名" />
			// 		),
			// 		cell: ({ row }) => {
			// 			const account = row.getValue("account") as string;
			// 			return (
			// 				<div className="min-w-[120px] flex space-x-2">
			// 					{account}
			// 					{row.original.account_is_admin && (
			// 						<StateTag
			// 							active={{
			// 								className:
			// 									" text-[#fff] leading-[18px] bg-[#065F46] text-xs ml-3",
			// 								desc: "管",
			// 							}}
			// 						/>
			// 					)}
			// 				</div>
			// 			);
			// 		},
			// 		enableSorting: false,
			// 		filterFn: (row, id, value) => {
			// 			return value.includes(row.getValue(id));
			// 		},
			// 	});
			// }

			return columnList;
		}, [orders, isWg, flags, exclusive_num]);

		const changePathHandle = (value: boolean) => {
			setResult(null);
			setHintOpen(value);
		};

		useEffect(() => {
			onData(tableData);
		}, [tableData]);

		useEffect(() => {
			if (result === null) return;
			if (result) {
				setHintOpen(null);
			} else {
				setHintOpen(true);
			}
		}, [result]);

		useImperativeHandle(ref, () => ({
			clear,
			setOpenFormAlertDialog: (value: boolean) => {
				setOpen(value);
			},
			setTypeFormAlertDialog: (value: DialogConfig) => {
				setType(value);
			},
		}));

		return (
			<>
				<div className="flex rounded-md flex-1">
					{pathLinkLoading
						? (
							<div className="h-full w-full flex flex-col items-center justify-center">
								<p className="text-lg mt-[23px]">加载中...</p>
							</div>
						)
						: pathLinkError
						? (
							<div className="h-full w-full flex flex-col items-center justify-center space-y-4">
								<p className="text-lg text-red-500">
									加载链路失败
								</p>
								<p className="text-sm text-gray-500 text-center max-w-md">
									错误信息:{" "}
									{pathLinkError?.message || "网络连接错误"}
								</p>
								<div className="flex space-x-3">
									<Button
										variant="outline"
										onClick={() => window.location.reload()}
									>
										重新加载
									</Button>
									<Button
										className="bg-[#2851B2] text-[#fff] font-medium"
										onClick={() => setOpen(true)}
									>
										新建链路
									</Button>
								</div>
							</div>
						)
						: tableData.length > 0
						? (
							<DataTableMemo
								className=" px-4 p-1 pb-0"
								pagination={pagination}
								data={tableData}
								columns={columnsData}
								// loadMore={loadMore}
							>
								{{
									toolbar: (table) => (
										<SearchLocal
											table={table}
											options={userFilter}
											toolbar={
												<DataTableViewOptions
													table={table}
													columnName={UserManageEnum}
												/>
											}
										/>
									),
								}}
							</DataTableMemo>
						)
						: (
							<div className="h-full w-full flex flex-col items-center justify-center space-y-4">
								<p className="text-lg text-gray-600">
									尚未定义路线
								</p>
								<p className="text-sm text-gray-400 text-center max-w-md">
									您可以创建新的代理链路来开始使用多跳代理服务
								</p>
								<Button
									className="bg-[#2851B2] text-[#fff] font-medium px-6 py-2"
									onClick={() => setOpen(true)}
								>
									新建链路
								</Button>
							</div>
						)}
				</div>
				<FormAlertDialog {...dialogProps} />
				<UseDialog
					open={useVisible}
					title={"确定应用【" + useData?.name + "】这条链路?"}
					desc="应用后即刻生效，此链路将替代使用中链路！"
					loading={useLoading}
					openChange={setUseVisible}
					useHandle={useHandle}
					className="w-[600px]"
				/>
				<DeleteDialog
					open={delVisible}
					title={"确定删除【" + deleteData?.name + "】这条链路?"}
					desc="链路将从列表中删除，删除后不存在该条链路记录"
					loading={deleteLoading}
					openChange={setDelVisible}
					deleteHandle={deleteHandle}
					className="w-[600px]"
				/>

				<PathTosat
					exclusive_num={exclusive_num}
					open={hintOpen === null ? false : hintOpen}
					openChange={changePathHandle}
				/>
			</>
		);
	},
);
