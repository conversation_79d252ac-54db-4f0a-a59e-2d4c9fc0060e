import { Button } from "@/components/ui/button";
import { useEffect, useMemo, useRef, useState } from "react";
// import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TABPROXIESTYPE } from "./types/config";
import { DIALOGTYPE } from "./data/type";
import _ from "lodash";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import { BaseContent } from "@/components/BaseContent";
import {
  GeneralModeTable,
  GeneralModeTableRef,
} from "./components/generalModeTable";
import { SpecialModeTable } from "./components/specialModeTable";
import { PathList } from "./types/proxy-bridge-type";
// import { isAdmin } from "@/auth"
import { cn } from "@/lib/utils";
import { api } from "@/auth.tsx";
import { toast } from "@/components/ui/use-toast";
import { useQueryClient } from "@tanstack/react-query";

export interface LinkType {
  contry: string;
  flag: string;
  exclusive?: string;
  disabled?: boolean;
  hideExclusiveStyle?: boolean;
}

export interface DialogProps extends PathList {
  name: string;
  nodeGuard: string;
  nodeExit: string;
  nodeDefault: string;
  use: boolean;
  exclusive: string | boolean;
}

export const tabProxiesList = [
  {
    key: TABPROXIESTYPE.GENERAL,
    value: "通用模式",
  },
  {
    key: TABPROXIESTYPE.SPECIAL,
    value: "特殊模式",
  },
];

export const SearchOptionText = (number_: number) => {
  return (
    <span
      className={cn(
        " text-sm font-normal mr-2",
        number_ > 0 ? "text-[#71717A]" : "text-[#DC2626]",
      )}
    >
      {number_ > 0 ? number_ + "ms" : "超时"}
    </span>
  );
};

export const Link = () => {
  // const [tabChoose, setTabChoose] = useState(TABPROXIESTYPE.GENERAL)
  const { globalInfo, refetchGlobalInfo } = useGlobalInfoContext();
  const [tableData, setTableData] = useState<PathList[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const generalModeTableRef = useRef<GeneralModeTableRef>(null);
  const queryClient = useQueryClient();

  const isWg = useMemo(() => false, [globalInfo]); // WG functionality removed

  const hanleClickAddLink = () => {
    if (generalModeTableRef.current) {
      generalModeTableRef.current.clear();
      generalModeTableRef.current.setTypeFormAlertDialog(DIALOGTYPE.ADD);
      generalModeTableRef.current.setOpenFormAlertDialog(true);
    }
  };

  const handleOneClickGenerate = async () => {
    setIsGenerating(true);
    try {
      const response = await api.post({}, "/path/generate").json() as any;

      if (response.code === 200) {
        toast({
          title: "一键建链成功",
          description: `已创建链路: ${response.data.path}`,
        });

        // Manually refresh the pathList query to update the table
        await queryClient.invalidateQueries({ queryKey: ["pathList"] });
        refetchGlobalInfo();
      } else {
        throw new Error(response.msg || "一键建链失败");
      }
    } catch (error) {
      console.error("One-click generate failed:", error);
      toast({
        title: "一键建链失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    refetchGlobalInfo();
  }, []);

  return (
    <BaseContent
      toolbar={!isWg &&
        !!tableData.length && (
        <div className="flex gap-2">
          <Button
            variant="link"
            onClick={() => {
              hanleClickAddLink();
            }}
          >
            新增链路
          </Button>
          <Button
            variant="outline"
            onClick={handleOneClickGenerate}
            disabled={isGenerating}
          >
            {isGenerating ? "生成中..." : "一键建链"}
          </Button>
        </div>
      )}
      // titleContain={
      //     isAdmin() && <>
      //         <div className="flex space-x-5 items-center">
      //             <Tabs defaultValue={tabChoose} className="w-full rounded-lg">
      //                 <TabsList className="w-full flex justify-start bg-[#FAFAFA] ">
      //                     {
      //                         tabProxiesList.map(item => (
      //                             <TabsTrigger className="flex space-x-2" key={item.key} value={item.key} onClick={() => {
      //                                 setTabChoose(item.key);
      //                             }}>
      //                                 <span>{item.value}</span>
      //                             </TabsTrigger>
      //                         ))
      //                     }
      //                 </TabsList>
      //             </Tabs>
      //         </div>
      //     </>
      // }
      title="链路"
    >
      <>
        {!isWg
          ? (
            <GeneralModeTable
              ref={generalModeTableRef}
              onData={(data: PathList[]) => setTableData(data)}
            />
          )
          : <SpecialModeTable />}
      </>
    </BaseContent>
  );
};
