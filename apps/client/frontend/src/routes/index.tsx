import { createFileRoute, Navigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { ActivationPage } from "@/pages/activation";

function IndexComponent() {
  const [isActivated, setIsActivated] = useState<boolean | null>(null);

  useEffect(() => {
    // Check activation status
    fetch("/api/status")
      .then((res) => res.json())
      .then((data) => {
        const typedData = data as { activated: boolean };
        setIsActivated(typedData.activated);
      })
      .catch(() => {
        // If API fails, assume not activated
        setIsActivated(false);
      });
  }, []);

  if (isActivated === null) {
    // Loading state
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600">
        </div>
      </div>
    );
  }

  if (!isActivated) {
    // Show activation page instead of redirecting
    return <ActivationPage />;
  }

  return <Navigate to="/dashboard" />;
}

export const Route = createFileRoute("/")({
  component: IndexComponent,
});
